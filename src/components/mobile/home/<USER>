import React, { memo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, I18nManager, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { CategoriesLoader } from '../../loader-mobile';
import { useAppSelector } from '../../../redux/hooks';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../constants';
import { colors, fonts } from '../../../utils/theme';

const BrowseCategories = memo(() => {
	const { t, i18n } = useTranslation();
	const navigation = useNavigation<any>();
	const { loadingFamily, categoryDetails } = useAppSelector(state => state.catalog);

	const getFamilyName = (item: any) => {
		const language = i18n.language;
		return language === 'en' ? item.category_name : item.secondary_language_category_name;
	};

	const onClickFamily = useCallback((familyId: string) => {
		navigation.navigate('Catalog', { familyId });
	}, []);

	const renderCategoryCard = useCallback(({ item, index }: any) => {
		const isFirst = index === 0;
		return (
			<TouchableOpacity
				style={[styles.categoryCard, isFirst && styles.categoryFirstItem]}
				onPress={() => onClickFamily(item._id)}
			>
				<Text style={styles.prodcutTitle} numberOfLines={2}>{getFamilyName(item)}</Text>
				<Text style={styles.productPrice}>{item.totalProducts} {t('items')}</Text>
			</TouchableOpacity>
		);
	}, []);

	const keyExtractor = useCallback((item: any) => item._id, []);

	if (loadingFamily) {
		return <CategoriesLoader />;
	}

	if (categoryDetails.length === 0) {
		return null;
	}

	return (
		<>
			<Text style={styles.listHeader}>{t('browse_categories')}</Text>
			<FlatList
				horizontal
				data={categoryDetails}
				renderItem={renderCategoryCard}
				keyExtractor={keyExtractor}
				contentContainerStyle={styles.listContainer}
				showsHorizontalScrollIndicator={false}
			/>
		</>
	);
});

const styles = StyleSheet.create({
	listContainer: {
		paddingHorizontal: 14,
		marginTop: VERTICAL_DIMENS._16,
		paddingBottom: VERTICAL_DIMENS._8
	},
	listHeader: {
		alignSelf: 'flex-start',
		color: colors.primary,
		marginLeft: HORIZONTAL_DIMENS._16,
		marginTop: VERTICAL_DIMENS._20,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	categoryCard: {
		backgroundColor: colors.white,
		borderRadius: 16,
		paddingHorizontal: HORIZONTAL_DIMENS._18,
		paddingVertical: VERTICAL_DIMENS._12,
		marginLeft: VERTICAL_DIMENS._8,
		shadowColor: colors.black,
		shadowOffset: {
			width: 0,
			height: 1
		},
		shadowOpacity: 0.18,
		shadowRadius: 1,
		elevation: 1,
		width: HORIZONTAL_DIMENS._161,
		height: VERTICAL_DIMENS._76
	},
	categoryFirstItem: {
		marginLeft: 0
	},
	prodcutTitle: {
		color: colors.darkGray,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._14,
		height: VERTICAL_DIMENS._36,
		//writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr',
		alignSelf: 'flex-start'
	},
	productPrice: {
		color: colors.grey400,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._10,
		marginTop: 4,
		alignSelf: 'flex-start'
	}
});

export { BrowseCategories };