import React from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, I18nManager, StyleSheet, Text, View } from 'react-native';
import { ProgressBar } from '../../common';
import { DealCardMobile } from '../../product-cards';
import { ArrowRight } from '../../../assets/svgs/icons';
import { HORIZONTAL_DIMENS, ProductType, recommendedProducts } from '../../../constants';
import { colors, fonts } from '../../../utils/theme';

type FlatListItem = {
	item: ProductType;
	index: number;
};

const DealsHorizontal = () => {
	const { t } = useTranslation();

	const renderProductCard = ({ item, index }: FlatListItem) => {
		const isEnd = index === recommendedProducts.length - 1;
		return (
			<DealCardMobile
				item={item}
				containerStyle={isEnd && styles.productLastItem}
			/>
		);
	};

	return (
		<>
			<View style={styles.dealCardTitleView}>
				<Text style={styles.dealCardTitle}>Summer deals</Text>
				<ArrowRight style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />
			</View>
			<View style={styles.progressContainer}>
				<View style={styles.progressBar}>
					<ProgressBar percentage={'15%'} />
				</View>
				<Text style={styles.progressText}>60 {t('days_left')}</Text>
			</View>
			<FlatList
				keyExtractor={(item, index) => `${item.name}${index}`}
				horizontal
				data={recommendedProducts}
				renderItem={renderProductCard}
				showsHorizontalScrollIndicator={false}
				contentContainerStyle={styles.listContainer}
			/>
		</>
	);
};

const styles = StyleSheet.create({
	listContainer: {
		paddingHorizontal: 16,
		marginTop: 12,
		paddingBottom: 20
	},
	productLastItem: {
		marginRight: 0
	},
	dealCardTitleView: {
		alignItems: 'center',
		flexDirection: 'row',
		marginLeft: 16,
		marginTop: 20
	},
	dealCardTitle: {
		color: colors.darkGray,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	progressContainer: {
		alignItems: 'center',
		flexDirection: 'row',
		height: 24,
		marginTop: 4,
		marginHorizontal: 16
	},
	progressBar: {
		flex: 7
	},
	progressText: {
		color: colors.primary,
		flex: 3,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._14,
		lineHeight: 17.07,
		textAlign: 'right',
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	}
});

export { DealsHorizontal };
