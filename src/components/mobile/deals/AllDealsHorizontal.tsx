import React, { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { differenceInDays } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { FlatList, I18nManager, StyleSheet, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { DealCardMobile } from '../../product-cards';
import { CustomPressable, ProgressBar } from '../../common';
import { ArrowRightLong } from '../../../assets/svgs/icons';
import { colors, fonts } from '../../../utils/theme';
import { HORIZONTAL_DIMENS, PRODUCT_TYPES, VERTICAL_DIMENS } from '../../../constants';
import { setSelectedGroup, setSelectedProduct, setSelectedvariant, setSelectedvariantID } from '../../../redux/features/productDetails-slice';

const AllDealsHorizontal = (props: any) => {
	const { deals } = props;
	const dispatch = useDispatch();
	const { t, i18n } = useTranslation();
	const navigation = useNavigation<NativeStackNavigationProp<any>>();

	// const deals = useAppSelector(getSelectedDealItems);

	const diffInDays = differenceInDays(new Date(deals?.deal_to_date), new Date());
	const tillDays = differenceInDays(new Date(), new Date(deals?.deal_from_date));
	const totalDays = differenceInDays(new Date(deals?.deal_to_date), new Date(deals?.deal_from_date));

	const totalPercent = (tillDays * 100) / totalDays;

	const handleNavigation = useCallback((item: any) => {
		const itemId = item?.product?.type === PRODUCT_TYPES.VARIANT ? item?.product?.parent_product?._id : item?.product?._id;
		if (item?.product?.variant_id) {
			const str = item?.product?.variant_id;
			const res = str.split('_');
			res.length === 3 && dispatch(setSelectedGroup(res[2]));
			dispatch(setSelectedvariant(res[1]));
		}
		dispatch(setSelectedvariantID(item?.product?._id));
		dispatch(setSelectedProduct(itemId));
		navigation.navigate('CatalogVariant', { itemId: itemId });
	}, []);

	const renderProductCard = useCallback(({ item, index }: any) => {
		const isEnd = index === deals.length - 1;
		return (
			<DealCardMobile
				item={item}
				containerStyle={[styles.productCard, isEnd && styles.productLastItem]}
				dealType={deals.deal_type}
				onPress={handleNavigation}
			/>
		);
	}, []);

	const openDealsPage = useCallback(() => {
		dispatch(setSelectedProduct(''));  // Because if a product is already selected while navigating to the deals page, it will automatically scroll to that product.
		navigation.navigate('Deals', { dealId: deals._id, isFromCard: true });
	}, []);

	const keyExtractor = useCallback((item: any, index: number) => `${item.name}-${index}`, []);

	return (
		<>
			<View style={styles.dealCardTitleView}>
				<View style={styles.headerTitleView}>
					<Text style={styles.dealCardTitle}>{i18n.language === 'en' ? deals?.deal_name : deals?.secondary_deal_name}</Text>
				</View>

				<CustomPressable
					style={styles.viewAllButton}
					onPress={openDealsPage}
				>
					<Text style={styles.viewAllText}>{t('view_all')}</Text>
					<ArrowRightLong fill={colors.secondary} style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />
				</CustomPressable>


			</View>
			<View style={styles.progressContainer}>
				<View style={styles.progressBar}>
					<ProgressBar percentage={`${totalPercent}%`} containerStyle={{ backgroundColor: colors.white }} />
				</View>
				<Text style={styles.progressText}>{diffInDays + ' ' + t('days_left')}</Text>
			</View>
			<FlatList
				keyExtractor={keyExtractor}
				horizontal
				data={deals.deal_products}
				renderItem={renderProductCard}
				showsHorizontalScrollIndicator={false}
				contentContainerStyle={styles.listContainer}
			/>
		</>
	);
};

const styles = StyleSheet.create({
	listContainer: {
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		marginTop: VERTICAL_DIMENS._12,
		paddingBottom: VERTICAL_DIMENS._20
	},
	productCard: {
		height: VERTICAL_DIMENS._225
		// backgroundColor:'lightgreen'
	},
	productLastItem: {
		marginRight: 0
	},
	dealCardTitleView: {
		alignItems: 'center',
		flexDirection: 'row',
		marginHorizontal: HORIZONTAL_DIMENS._16,
		marginTop: VERTICAL_DIMENS._20,
		justifyContent: 'space-between'
	},
	headerTitleView: {
		flexDirection: 'row'
	},
	dealCardTitle: {
		color: colors.darkGray,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	viewAllButton: {
		alignItems: 'center',
		flexDirection: 'row'
	},
	viewAllText: {
		color: colors.secondary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._14,
		marginRight: 12,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	progressContainer: {
		alignItems: 'center',
		flexDirection: 'row',
		height: VERTICAL_DIMENS._24,
		marginTop: VERTICAL_DIMENS._4,
		marginHorizontal: HORIZONTAL_DIMENS._16
	},
	progressBar: {
		flex: 7
	},
	progressText: {
		color: colors.primary,
		flex: 3,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._14,
		lineHeight: 17.07,
		textAlign: 'right',
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	}
});

export { AllDealsHorizontal };
