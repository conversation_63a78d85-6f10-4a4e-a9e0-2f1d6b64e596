import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, I18nManager, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { ProgressBar } from '../../common';
import { DealCardMobile } from '../../product-cards';
import { ArrowRight } from '../../../assets/svgs/icons';
import { colors, fonts } from '../../../utils/theme';
import { getSelectedDealItems } from '../../../redux/selectors';
import { useAppSelector } from '../../../redux/hooks';
import { differenceInDays } from 'date-fns';
import { HORIZONTAL_DIMENS, PRODUCT_TYPES, VERTICAL_DIMENS } from '../../../constants';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useNavigation } from '@react-navigation/native';
import { setSelectedGroup, setSelectedProduct, setSelectedvariant, setSelectedvariantID } from '../../../redux/features/productDetails-slice';
import { useDispatch } from 'react-redux';

const DealsHorizontal = () => {
	const { t, i18n } = useTranslation();
	const navigation = useNavigation<NativeStackNavigationProp<any>>();
	const dispatch = useDispatch();

	const deals = useAppSelector(getSelectedDealItems);

	const diffInDays = differenceInDays(new Date(deals?.deal_to_date), new Date());
	const tillDays = differenceInDays(new Date(), new Date(deals?.deal_from_date));
	const totalDays = differenceInDays(new Date(deals?.deal_to_date), new Date(deals?.deal_from_date));

	const totalPercent = (tillDays * 100) / totalDays;

	const openDeals = useCallback(() => {
		navigation.navigate('Deals', { dealId: deals._id, isFromCard: true });
	}, [deals._id]);

	const handleNavigation = useCallback((item: any) => {
		const itemId = item?.product?.type === PRODUCT_TYPES.VARIANT ? item?.product?.parent_product?._id : item?.product?._id;

		if (item?.product?.variant_id) {
			const str = item?.product?.variant_id;
			const res = str.split('_');
			res.length === 3 && dispatch(setSelectedGroup(res[2]));

			dispatch(setSelectedvariant(res[1]));
		}
		dispatch(setSelectedvariantID(item?.product?._id));
		dispatch(setSelectedProduct(itemId));

		navigation.navigate('CatalogVariant', { itemId: itemId });
	}, []);

	const renderProductCard = useCallback(({ item, index }: any) => {
		const isEnd = index === deals.length - 1;
		return (
			<DealCardMobile
				item={item}
				containerStyle={isEnd && styles.productLastItem}
				dealType={deals.deal_type}
				onPress={handleNavigation}
			/>
		);
	}, [deals]);

	const keyExtractor = useCallback((item: any, index: number) => `${item.name}${index}`, []);

	return (
		<>
			<TouchableOpacity style={styles.dealCardTitleView} onPress={openDeals}>
				<Text style={styles.dealCardTitle}>{i18n.language === 'en' ? deals?.deal_name : deals?.secondary_deal_name}</Text>
				<ArrowRight style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />
			</TouchableOpacity>
			<View style={styles.progressContainer}>
				<View style={styles.progressBar}>
					<ProgressBar percentage={`${totalPercent}%`} />
				</View>
				<Text style={styles.progressText}>{diffInDays + t('days_left')}</Text>
			</View>
			<FlatList
				keyExtractor={keyExtractor}
				horizontal
				data={deals.deal_products}
				renderItem={renderProductCard}
				showsHorizontalScrollIndicator={false}
				contentContainerStyle={styles.listContainer}
			/>
		</>
	);
};

const styles = StyleSheet.create({
	listContainer: {
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		marginTop: VERTICAL_DIMENS._12,
		paddingBottom: VERTICAL_DIMENS._20
	},
	productLastItem: {
		marginRight: 0
	},
	dealCardTitleView: {
		alignItems: 'center',
		flexDirection: 'row',
		marginLeft: HORIZONTAL_DIMENS._16,
		marginTop: VERTICAL_DIMENS._20
	},
	dealCardTitle: {
		color: colors.darkGray,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	progressContainer: {
		alignItems: 'center',
		flexDirection: 'row',
		height: VERTICAL_DIMENS._24,
		marginTop: VERTICAL_DIMENS._4,
		marginHorizontal: HORIZONTAL_DIMENS._16
	},
	progressBar: {
		flex: 7
	},
	progressText: {
		color: colors.primary,
		flex: 3,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._14,
		lineHeight: 17.07,
		textAlign: 'right',
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	}
});

export { DealsHorizontal };
