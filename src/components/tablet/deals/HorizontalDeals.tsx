import React from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, I18nManager, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useNavigation } from '@react-navigation/native';
import TabButton from '../../common/buttons/TabButton';
import { ArrowRightLong } from '../../../assets/svgs/icons';
import { colors, fonts } from '../../../utils/theme';
import { CardDeals } from '../../product-cards';
import { useAppSelector } from '../../../redux/hooks';
import { getSelectedDealItems } from '../../../redux/selectors';
import { useDispatch } from 'react-redux';
import { setSelectedDealName } from '../../../redux/features/deals-slice';
import { HORIZONTAL_DIMENS, PRODUCT_TYPES } from '../../../constants';
import { setSelectedGroup, setSelectedProduct, setSelectedvariant, setSelectedvariantID } from '../../../redux/features/productDetails-slice';

type FlastListItem = {
	item: any;
	index: number;
};

const HorizontalDeals = () => {
	const { t, i18n } = useTranslation();
	const dispatch = useDispatch();

	const navigation = useNavigation<NativeStackNavigationProp<any>>();

	const deals = useAppSelector(getSelectedDealItems);
	const selectedDeal = useAppSelector(state => state.deal.selectedDeal);
	const dealsName = useAppSelector(state => state.deal.dealsName);

	const renderProductCard = ({ item, index }: FlastListItem) => {
		const isEnd = index === deals.length - 1;

		const handleNavigation = () => {
			const itemId = item?.product?.type === PRODUCT_TYPES.VARIANT ? item?.product?.parent_product?._id : item?.product?._id;

			if (item?.product?.variant_id) {
				const str = item?.product?.variant_id;
				const res = str.split('_');
				res.length === 3 && dispatch(setSelectedGroup(res[2]));

				dispatch(setSelectedvariant(res[1]));
			}
			dispatch(setSelectedvariantID(item?.product?._id));
			dispatch(setSelectedProduct(itemId));

			navigation.navigate('CatalogVariant', { productId: itemId });
		};

		return (
			<CardDeals
				item={item}
				isLast={isEnd}
				dealType={deals?.deal_type}
				onPress={handleNavigation}
			/>
		);
	};

	const viewAll = () => {
		navigation.navigate('Deals');
	};

	return (
		<>
			<View style={styles.listHeaderContainer}>
				<View style={styles.headerTitleView}>
					<Text style={styles.listHeaderText}>{t('latest_deals')}</Text>
				</View>
				<View style={styles.tabsView}>
					{
						dealsName?.map((tab: any) => (
							<TabButton
								key={tab.deal_name}
								style={styles.tabButton}
								title={'#' + i18n.language === 'en' ? tab?.deal_name : tab?.secondary_deal_name}
								onPress={() => dispatch(setSelectedDealName(tab))}
								active={selectedDeal?.deal_name === tab?.deal_name} icon={undefined} />
						))
					}
					<TouchableOpacity style={styles.viewAllButton} onPress={viewAll}>
						<Text style={styles.viewAllText}>{t('view_all')}</Text>
						<ArrowRightLong fill={colors.secondary} style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />
					</TouchableOpacity>
				</View>
			</View>
			<FlatList
				horizontal
				bounces={false}
				data={deals?.deal_products}
				renderItem={renderProductCard}
				keyExtractor={(item) => item._id}
				contentContainerStyle={styles.listContainer}
				showsHorizontalScrollIndicator={false}
			/>
		</>
	);
};

const styles = StyleSheet.create({
	listContainer: {
		paddingLeft: 30,
		paddingBottom: 13
	},
	listHeaderContainer: {
		alignItems: 'center',
		flexDirection: 'row',
		justifyContent: 'space-between',
		paddingHorizontal: 30,
		paddingVertical: 14
	},
	headerTitleView: {
		flexDirection: 'row'
	},
	listHeaderText: {
		paddingLeft: 6,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	tabsView: {
		alignItems: 'center',
		flexDirection: 'row'
	},
	tabButton: {
		marginRight: 20
	},
	viewAllButton: {
		alignItems: 'center',
		flexDirection: 'row'
	},
	viewAllText: {
		color: colors.secondary,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._14,
		marginRight: 12,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	}
});

export { HorizontalDeals };