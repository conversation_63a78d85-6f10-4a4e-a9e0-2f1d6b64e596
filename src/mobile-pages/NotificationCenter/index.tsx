import React, { useEffect } from 'react';
import {
	View,
	Text,
	FlatList,
	ActivityIndicator,
	RefreshControl,
	Pressable,
	Image
} from 'react-native';
import { getNotificationList } from '../../redux/apis/notification';
import { useAppDispatch, useAppSelector } from '../../redux/hooks';
import usePaginatedAPICall from '../../hooks/usePaginatedAPICall';
import { colors } from '../../utils/theme';
import styles from './styles';
import {
	DollarNotify,
	NewProductNotify,
	NotificationBell,
	OrderNotify,
	RewardNotify
} from '../../assets/svgs/icons';
import { formatDistanceToNow } from 'date-fns';
import { markAllReadNotification } from '../../redux/features/notification-slice';
import useNotifications from '../../hooks/useNotifications';
import { Trans } from 'react-i18next';
import i18n from '../../locales/i18n';
import { ar, enUS } from 'date-fns/locale';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { getUserType } from '../../redux/selectors';
import { USER_TYPES } from '../../constants';
import { setTransactionList } from '../../redux/features/payment-slice';
import { NotificationType } from '../../types/notification';
const NotificationIcon = ({ type }: { type: string }) => {
	switch (type) {
		case 'NEW_PAYMENT':
			return <DollarNotify />;

		case 'NEW_PRODUCTS':
			return <NewProductNotify />;

		case 'NEW_ORDER':
		case 'ORDER_RECEIVED':
		case 'ORDER_PREPARING':
		case 'ORDER_SHIPPED':
		case 'ORDER_DELIVERED':
		case 'ORDER_FOR_APPROVAL':
		case 'ORDER_APPROVED':
			return <OrderNotify />;

		case 'REWARD_PAYMENT':
		case 'REWARD_PURCHASE':
		case 'REWARD_SALESPERSON_VISIT':
		case 'REWARD_MILESTONE_ONE':
		case 'REWARD_MILESTONE_TWO':
		case 'REWARD_MILESTONE_THREE':
		case 'REWARD_MEMBER_SCAN':
			return <RewardNotify />;

		default:
			return <NotificationBell />;
	}
};
const NotificationCenter = () => {
	const dispatch = useAppDispatch();
	const currentRole = useAppSelector((state) => state.auth.currentRole);
	const tenantId = currentRole?.tenant_id?._id;
	const userRoleId = currentRole?._id;
	const walletDetails = useAppSelector((state) => state.payment.walletDetails);
	const currency = useAppSelector(
		(state) => state.auth.currentRole.tenant_id?.country.currency
	);
	const { clearAllNotifications, syncBadgeCount } = useNotifications();
	const navigation = useNavigation<any>();
	const userType = useAppSelector(getUserType);
	const rewardType = useAppSelector((state) => state.reward.membershipType);
	// Define fetchData function for pagination hook
	useEffect(() => {
		dispatch(markAllReadNotification());
		clearAllNotifications();
		// Sync badge count after marking notifications as read
		setTimeout(() => {
			syncBadgeCount();
		}, 500);
	}, []);

	const fetchDataFn = async (pageNumber: number) => {
		const rawResponse = await dispatch(
			getNotificationList({
				tenantId,
				perPage: 20,
				page: pageNumber,
				userRoleId
			})
		).unwrap();
		return rawResponse?.data; // Ensure the hook gets the correct response structure
	};

	// Use the pagination hook
	const { data, loading, refreshing, handleEndReached, handleRefresh } =
		usePaginatedAPICall(fetchDataFn);

	const handleNavigation = (item: NotificationType) => {
		// console.log('🚀 ~ handleNavigation ~ item.type:', item.type);

		const isWalletPresent = !!walletDetails?._id;

		switch (userType) {
			case USER_TYPES.CUSTOMER_APP:
				switch (item.type) {
					case 'NEW_PAYMENT':
						dispatch(setTransactionList([]));
						navigation.navigate('Transactions', { isWalletPresent });
						break;

					case 'NEW_PRODUCTS':
						navigation.navigate('NewProducts');
						break;

					case 'NEW_ORDER':
					case 'ORDER_RECEIVED':
					case 'ORDER_PREPARING':
					case 'ORDER_SHIPPED':
					case 'ORDER_DELIVERED':
					case 'ORDER_APPROVED':
					case 'ORDER_FOR_APPROVAL':
						navigation.navigate('TrackOrder', {
							orderId: item.payload_data?._id
						});
						break;

					case 'REWARD_PAYMENT':
					case 'REWARD_PURCHASE':
					case 'REWARD_MEMBER_SCAN':
						navigation.navigate('RewardTransactions');
						break;

					case 'REWARD_SALESPERSON_VISIT':
						navigation.navigate('Account');
						break;

					case 'REWARD_MILESTONE_ONE':
					case 'REWARD_MILESTONE_TWO':
					case 'REWARD_MILESTONE_THREE':
						navigation.navigate('RewardProgram', { rewardType });
						break;

					default:
						navigation.navigate('NotificationCenter');
				}
				break;

			case USER_TYPES.SALES_APP:
			case USER_TYPES.SUPERVISOR_APP:
			case USER_TYPES.TENANT_PORTAL:
			case USER_TYPES.TENANT_OWNER:
			case USER_TYPES.ADMIN:
				switch (item.type) {
					case 'NEW_PAYMENT':
						dispatch(setTransactionList([]));
						navigation.navigate('Transactions', { isWalletPresent });
						break;

					case 'NEW_PRODUCTS':
						navigation.navigate('NewProducts');
						break;

					case 'NEW_ORDER':
					case 'ORDER_RECEIVED':
					case 'ORDER_PREPARING':
					case 'ORDER_SHIPPED':
					case 'ORDER_DELIVERED':
					case 'ORDER_APPROVED':
					case 'ORDER_FOR_APPROVAL':
						navigation.navigate('TrackOrder', {
							orderId: item.payload_data?._id
						});
						break;

					case 'REWARD_PAYMENT':
					case 'REWARD_PURCHASE':
					case 'REWARD_SALESPERSON_VISIT':
					case 'REWARD_MILESTONE_ONE':
					case 'REWARD_MILESTONE_TWO':
					case 'REWARD_MILESTONE_THREE':
						navigation.navigate('NotificationCenter');
						break;

					default:
						navigation.navigate('NotificationCenter');
				}
				break;
		}
	};

	const renderImageItem = ({ item }: any) => {
		return (
			<Image
				source={{
					uri: typeof item === 'string' ? item : item?.image_name
				}}
				resizeMode="contain"
				style={styles.imageStyle}
			/>
		);
	};
	const rendeItem = ({ item }: { item: NotificationType }) => {
		return (
			<View
				style={[
					styles.itemParentContainer,
					!item.is_read && { backgroundColor: colors.baby_blue }
				]}
			>
				<View style={styles.itemWrapper}>
					<Pressable
						style={styles.itemContainer}
						onPress={() => handleNavigation(item)}
					>
						<View style={styles.iconContainer}>
							<NotificationIcon type={item.type} />
						</View>
						<View style={styles.notifyDataContainer}>
							<Text style={styles.title}>
								{/* { item.message} */}
								<Trans
									i18nKey={
										(i18n.language === 'en'
											? item.center_message
											: item.center_secondary_language_message) || item.message
									}
									values={{ ...item.payload_data, currency }}
									components={{ bold: <Text style={styles.boldText} /> }}
								/>
							</Text>

							<Text style={styles.notifyDate}>
								{formatDistanceToNow(new Date(item.created_at), {
									addSuffix: true,
									locale: i18n.language === 'en' ? enUS : ar
								})}
							</Text>
						</View>
					</Pressable>
					{item?.payload_data?.productImages && (
						<FlatList
							horizontal
							contentContainerStyle={styles.imageListContainer}
							data={item.payload_data?.productImages}
							renderItem={renderImageItem}
							showsHorizontalScrollIndicator={false}
							nestedScrollEnabled
							keyboardShouldPersistTaps="handled"
						/>
					)}
				</View>
			</View>
		);
	};

	const renderFooter = () => {
		if (!loading) return null;
		return (
			<View>
				<ActivityIndicator size="small" color={colors.grey600} />
			</View>
		);
	};
	const insets = useSafeAreaInsets();
	return (
		<View style={[styles.container, { paddingBottom: insets.bottom }]}>
			{data?.length > 0 ? <FlatList
				data={data}
				contentContainerStyle={styles.flatlistContainer}
				nestedScrollEnabled
				renderItem={rendeItem}
				keyExtractor={(item, index) => item._id || index.toString()}
				onEndReached={handleEndReached}
				onEndReachedThreshold={0.5}
				refreshControl={
					<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
				}
				ListFooterComponent={renderFooter}
			/> : loading ? <View style={styles.loaderContainer}><ActivityIndicator size="large" color={colors.grey600} /></View> : <></>}
		</View>
	);
};

export default NotificationCenter;
